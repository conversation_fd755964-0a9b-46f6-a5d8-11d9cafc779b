// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
  output   = "../app/generated/prisma"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Restaurant {
  id          String  @id @default(cuid())
  name        String
  description String?
  address     String
  phone       String?
  website     String?
  email       String?

  // Google Places data
  googlePlaceId     String? @unique
  googleRating      Float?
  googleReviewCount Int?    @default(0)

  // Location data
  latitude  Float
  longitude Float
  city      String
  state     String?
  country   String  @default("US")
  zipCode   String?

  // Restaurant details
  cuisine            String[]
  priceLevel         Int? // 1-4 scale
  isHalal            Boolean  @default(true)
  halalCertification String? // Certification body
  isVerified         Boolean  @default(false)

  // Operating hours (JSON format)
  openingHours Json?

  // Media
  photos RestaurantPhoto[]

  // Relationships
  reviews   Review[]
  addedBy   User?    @relation("AddedRestaurants", fields: [addedById], references: [id])
  addedById String?

  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([latitude, longitude])
  @@index([city])
  @@index([isHalal])
  @@index([isVerified])
  @@index([googlePlaceId])
}

model RestaurantPhoto {
  id        String  @id @default(cuid())
  url       String
  caption   String?
  isPrimary Boolean @default(false)

  // Google Photos data
  googlePhotoReference String?
  width                Int?
  height               Int?

  restaurant   Restaurant @relation(fields: [restaurantId], references: [id], onDelete: Cascade)
  restaurantId String

  uploadedBy   User?   @relation(fields: [uploadedById], references: [id])
  uploadedById String?

  createdAt DateTime @default(now())

  @@index([restaurantId])
  @@index([isPrimary])
}

model User {
  id     String  @id @default(cuid())
  email  String  @unique
  name   String
  avatar String?

  // User preferences
  preferredCuisines String[]
  location          String?

  // Relationships
  reviews          Review[]
  addedRestaurants Restaurant[]      @relation("AddedRestaurants")
  photos           RestaurantPhoto[]

  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([email])
}

model Review {
  id      String  @id @default(cuid())
  rating  Int // 1-5 scale
  title   String?
  content String

  // Review details
  visitDate     DateTime?
  isRecommended Boolean?

  // Relationships
  restaurant   Restaurant @relation(fields: [restaurantId], references: [id], onDelete: Cascade)
  restaurantId String

  author   User   @relation(fields: [authorId], references: [id], onDelete: Cascade)
  authorId String

  // Moderation
  isApproved  Boolean   @default(true)
  moderatedAt DateTime?

  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([restaurantId, authorId]) // One review per user per restaurant
  @@index([restaurantId])
  @@index([authorId])
  @@index([rating])
  @@index([createdAt])
}
