import { Client } from "@googlemaps/google-maps-services-js";
import prisma from "./db.js";

// Initialize Google Maps client
const client = new Client({});

// Cache duration: 30 days (Google Places API limit)
const CACHE_DURATION_MS = 30 * 24 * 60 * 60 * 1000;

/**
 * Get cached Google Places data or fetch fresh data
 * @param {string} placeId - Google Places ID
 * @returns {Promise<Object|null>} Cached data or null if not found/expired
 */
async function getCachedPlaceData(placeId) {
  try {
    const cached = await prisma.googlePlacesCache.findUnique({
      where: { placeId },
    });

    if (!cached) return null;

    // Check if cache is expired
    if (new Date() > cached.expiresAt) {
      // Delete expired cache
      await prisma.googlePlacesCache.delete({
        where: { placeId },
      });
      return null;
    }

    return cached.googleData;
  } catch (error) {
    console.error("Error getting cached place data:", error);
    return null;
  }
}

/**
 * Cache Google Places data (30-day limit)
 * @param {string} placeId - Google Places ID
 * @param {Object} data - Google Places API response data
 */
async function cachePlaceData(placeId, data) {
  try {
    const expiresAt = new Date(Date.now() + CACHE_DURATION_MS);

    await prisma.googlePlacesCache.upsert({
      where: { placeId },
      update: {
        googleData: data,
        cachedAt: new Date(),
        expiresAt,
      },
      create: {
        placeId,
        googleData: data,
        expiresAt,
      },
    });
  } catch (error) {
    console.error("Error caching place data:", error);
  }
}

/**
 * Clean up expired cache entries (should be run periodically)
 */
export async function cleanupExpiredCache() {
  try {
    const result = await prisma.googlePlacesCache.deleteMany({
      where: {
        expiresAt: {
          lt: new Date(),
        },
      },
    });
    console.log(`Cleaned up ${result.count} expired cache entries`);
    return result.count;
  } catch (error) {
    console.error("Error cleaning up expired cache:", error);
    return 0;
  }
}

/**
 * Search for halal restaurants using Google Places API
 * @param {string} location - Location to search around (lat,lng or address)
 * @param {number} radius - Search radius in meters (default: 5000)
 * @param {string} keyword - Additional keyword for search (default: 'halal')
 * @returns {Promise<Array>} Array of restaurant data
 */
export async function searchHalalRestaurants(
  location,
  radius = 5000,
  keyword = "halal"
) {
  try {
    const response = await client.placesNearby({
      params: {
        location: location,
        radius: radius,
        type: "restaurant",
        keyword: `${keyword} restaurant`,
        key: process.env.GOOGLE_PLACES_API_KEY,
      },
    });

    return response.data.results.map((place) => ({
      placeId: place.place_id,
      name: place.name,
      address: place.vicinity,
      rating: place.rating,
      priceLevel: place.price_level,
      photos:
        place.photos?.map((photo) => ({
          photoReference: photo.photo_reference,
          width: photo.width,
          height: photo.height,
        })) || [],
      geometry: {
        lat: place.geometry.location.lat,
        lng: place.geometry.location.lng,
      },
      types: place.types,
      openingHours: place.opening_hours,
      isOpen: place.opening_hours?.open_now,
    }));
  } catch (error) {
    console.error("Error searching for restaurants:", error);
    throw new Error("Failed to search restaurants");
  }
}

/**
 * Get detailed information about a specific place
 * @param {string} placeId - Google Places ID
 * @returns {Promise<Object>} Detailed place information
 */
export async function getPlaceDetails(placeId) {
  try {
    // Check cache first
    const cachedData = await getCachedPlaceData(placeId);
    if (cachedData) {
      return cachedData;
    }

    // Fetch fresh data from Google Places API
    const response = await client.placeDetails({
      params: {
        place_id: placeId,
        fields: [
          "name",
          "formatted_address",
          "formatted_phone_number",
          "website",
          "rating",
          "reviews",
          "photos",
          "opening_hours",
          "price_level",
          "geometry",
          "types",
          "url",
        ],
        key: process.env.GOOGLE_PLACES_API_KEY,
      },
    });

    const place = response.data.result;
    const placeData = {
      placeId: placeId,
      name: place.name,
      address: place.formatted_address,
      phone: place.formatted_phone_number,
      website: place.website,
      rating: place.rating,
      priceLevel: place.price_level,
      photos:
        place.photos?.map((photo) => ({
          photoReference: photo.photo_reference,
          width: photo.width,
          height: photo.height,
        })) || [],
      reviews:
        place.reviews?.map((review) => ({
          author: review.author_name,
          rating: review.rating,
          text: review.text,
          time: review.time,
          profilePhoto: review.profile_photo_url,
        })) || [],
      openingHours: {
        periods: place.opening_hours?.periods || [],
        weekdayText: place.opening_hours?.weekday_text || [],
        isOpen: place.opening_hours?.open_now,
      },
      geometry: {
        lat: place.geometry?.location?.lat,
        lng: place.geometry?.location?.lng,
      },
      types: place.types,
      googleUrl: place.url,
    };

    // Cache the data for 30 days
    await cachePlaceData(placeId, placeData);

    return placeData;
  } catch (error) {
    console.error("Error getting place details:", error);
    throw new Error("Failed to get place details");
  }
}

/**
 * Search for places by text query
 * @param {string} query - Text search query
 * @param {string} location - Optional location bias
 * @returns {Promise<Array>} Array of search results
 */
export async function searchPlacesByText(query, location = null) {
  try {
    const params = {
      query: `${query} halal restaurant`,
      key: process.env.GOOGLE_PLACES_API_KEY,
    };

    if (location) {
      params.location = location;
      params.radius = 10000; // 10km radius when location is provided
    }

    const response = await client.textSearch({ params });

    return response.data.results.map((place) => ({
      placeId: place.place_id,
      name: place.name,
      address: place.formatted_address,
      rating: place.rating,
      priceLevel: place.price_level,
      photos:
        place.photos?.map((photo) => ({
          photoReference: photo.photo_reference,
          width: photo.width,
          height: photo.height,
        })) || [],
      geometry: {
        lat: place.geometry.location.lat,
        lng: place.geometry.location.lng,
      },
      types: place.types,
      openingHours: place.opening_hours,
      isOpen: place.opening_hours?.open_now,
    }));
  } catch (error) {
    console.error("Error searching places by text:", error);
    throw new Error("Failed to search places");
  }
}

/**
 * Get photo URL from photo reference
 * @param {string} photoReference - Google Places photo reference
 * @param {number} maxWidth - Maximum width of the photo
 * @returns {string} Photo URL
 */
export function getPhotoUrl(photoReference, maxWidth = 400) {
  return `https://maps.googleapis.com/maps/api/place/photo?maxwidth=${maxWidth}&photo_reference=${photoReference}&key=${process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY}`;
}

/**
 * Geocode an address to get coordinates
 * @param {string} address - Address to geocode
 * @returns {Promise<Object>} Coordinates and formatted address
 */
export async function geocodeAddress(address) {
  try {
    const response = await client.geocode({
      params: {
        address: address,
        key: process.env.GOOGLE_PLACES_API_KEY,
      },
    });

    if (response.data.results.length === 0) {
      throw new Error("Address not found");
    }

    const result = response.data.results[0];
    return {
      lat: result.geometry.location.lat,
      lng: result.geometry.location.lng,
      formattedAddress: result.formatted_address,
      placeId: result.place_id,
    };
  } catch (error) {
    console.error("Error geocoding address:", error);
    throw new Error("Failed to geocode address");
  }
}
