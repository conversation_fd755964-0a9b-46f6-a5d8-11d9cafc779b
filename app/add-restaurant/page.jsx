import AddRestaurantForm from "../components/AddRestaurantForm";
import {
  <PERSON>,
  Card<PERSON>ontent,
  CardHeader,
  CardTitle,
} from "../../components/ui/card";
import { ArrowLeft } from "lucide-react";
import Link from "next/link";
import { But<PERSON> } from "../../components/ui/button";

export default function AddRestaurantPage() {
  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="border-b bg-white/95 backdrop-blur supports-[backdrop-filter]:bg-white/60">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center space-x-4">
            <Link href="/">
              <Button variant="ghost" size="sm">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Search
              </Button>
            </Link>
            <div>
              <h1 className="text-2xl font-bold text-primary">
                Add New Restaurant
              </h1>
              <p className="text-muted-foreground">
                Help grow our halal restaurant directory
              </p>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="container mx-auto px-4 py-8">
        <div className="max-w-2xl mx-auto">
          <Card>
            <CardHeader>
              <CardTitle>Restaurant Information</CardTitle>
              <p className="text-muted-foreground">
                Please provide accurate information about the halal restaurant.
                All fields marked with * are required.
              </p>
            </CardHeader>
            <CardContent>
              <AddRestaurantForm />
            </CardContent>
          </Card>

          {/* Guidelines */}
          <Card className="mt-6">
            <CardHeader>
              <CardTitle className="text-lg">Submission Guidelines</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3 text-sm text-muted-foreground">
              <div className="flex items-start space-x-2">
                <span className="text-green-600 font-bold">✓</span>
                <span>Only submit restaurants that serve halal food</span>
              </div>
              <div className="flex items-start space-x-2">
                <span className="text-green-600 font-bold">✓</span>
                <span>Provide accurate contact information and address</span>
              </div>
              <div className="flex items-start space-x-2">
                <span className="text-green-600 font-bold">✓</span>
                <span>Include halal certification details if available</span>
              </div>
              <div className="flex items-start space-x-2">
                <span className="text-red-600 font-bold">✗</span>
                <span>Don't submit duplicate restaurants</span>
              </div>
              <div className="flex items-start space-x-2">
                <span className="text-red-600 font-bold">✗</span>
                <span>
                  Don't submit restaurants that are permanently closed
                </span>
              </div>
            </CardContent>
          </Card>
        </div>
      </main>
    </div>
  );
}
